//
//  PhoneIntegrationTests.swift
//  BriaVoip
//
//  Created by <PERSON> on 18.3.25.
//  Copyright © 2025 CounterPath Corporation Inc. All rights reserved.
//

@testable import Bria
import XCTest
import Contacts

// MARK: - Mock Types

class MockCpcContact: CpcContact {
	private let mockContactId: String?
	private var mockPhoneNumbers: [CNLabeledValue<CNPhoneNumber>]?
	
	override var identifier: String? {
		return mockContactId
	}
	
	override var phoneNumbers: [CNLabeledValue<CNPhoneNumber>]? {
		get {
			return mockPhoneNumbers
		}
		set {
			mockPhoneNumbers = newValue
		}
	}
	
	override init() {
		self.mockContactId = nil
		self.mockPhoneNumbers = []
		super.init()
	}
	
	init(contactId: String) {
		self.mockContactId = contactId
		self.mockPhoneNumbers = []
		super.init()
	}
}

class MockCpcContactPreview: CpcContactPreview {
	private let mockContactId: String?
	private let mockContactInternalId: Cpc<PERSON>ontactInternalId?
	
	override var identifier: String? {
		return mockContactId
	}
	
	init(contactId: String?, contactInternalId: CpcContactInternalId?) {
		self.mockContactId = contactId
		self.mockContactInternalId = contactInternalId
		super.init()
	}
}

struct CpcContactInternalId {
	let contactId: String
}

final class PhoneIntegrationTests: XCTestCase {
	func test_withEmptyNumberInput_hidesAddNumberButton() {
		let sut = makeSUT(dependency: .init(isAddNumberFeatureEnabled: true))

		XCTAssertTrue(sut.isAddNumberButtonHidden())
	}

	func test_withNumberInput_showsAddNumberButton() {
		let sut = makeSUT(dependency: .init(isAddNumberFeatureEnabled: true))

		sut.simulateNumberInput("123")

		XCTAssertFalse(sut.isAddNumberButtonHidden())
	}

	func test_tapOnButtonAddNumber_showsContextMenu() {
		let sut = makeSUT(dependency: .init(isAddNumberFeatureEnabled: true))

		XCTAssertNil(sut.currentActionSheet)

		sut.simulateNumberInput("123")
		sut.simulateAddNumberAction()

		assertAddNumberContextMenu(for: sut)
	}

	// MARK: - Delete Button Tests

	func test_deleteButtonPressed_withEmptyString_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.callNumberInput.text = ""

		XCTAssertNoThrow {
			sut.simulateDeleteButtonPress()
		}
	}

	func test_deleteButtonPressed_withSingleCharacter_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.callNumberInput.text = "1"
		sut.simulateDeleteButtonPress()

		XCTAssertNotNil(sut.callNumberInput.text)
	}

	func test_deleteButtonPressed_withSingleCharacterAsFirstResponder_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.callNumberInput.text = "1"
		sut.callNumberInput.becomeFirstResponder()

		sut.setCursorPosition(at: 1)
		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "")
	}

	func test_deleteButtonPressed_withProperlySetSelectedRange_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("123")

		sut.setCursorPosition(at: 3)
		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "12")
	}

	func test_deleteButtonPressed_withCursorAtBeginning_shouldDeleteCorrectly() {
		let sut = makeSUT()
		presentInWindow(sut)

		sut.simulateNumberInput("123")
		sut.setCursorPosition(at: 0)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "123")

		dismiss(sut)
	}

	func test_deleteButtonPressed_withCursorInMiddle_shouldDeleteCorrectly() {
		let sut = makeSUT()
		presentInWindow(sut)

		sut.simulateNumberInput("12345")

		sut.setCursorPosition(at: 2)
		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "1345")

		dismiss(sut)
	}

	func test_deleteButtonPressed_withSelectedRange_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("12345")
		sut.setTextSelection(from: 1, to: 4)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "15")
	}

	func test_deleteButtonPressed_withUnicodeCharacters_shouldDeleteCorrectly() {
		let sut = makeSUT()
		presentInWindow(sut)

		sut.simulateNumberInput("1👨2")
		sut.setCursorPosition(at: 2)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "12")

		dismiss(sut)
	}

	func test_deleteButtonPressed_withJoinedUnicodeCharacters_shouldDeleteCorrectly() {
		let sut = makeSUT()
		presentInWindow(sut)

		sut.simulateNumberInput("123👨‍👩‍👧‍👦456")
		sut.setCursorPosition(at: 8)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "123👨‍👩‍👧‍👦456")

		dismiss(sut)
	}

	func test_deleteButtonPressed_edgeCaseInvalidCursorPosition_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("1")
		sut.setCursorPosition(at: 2)

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "")
	}

	func test_deleteButtonPressed_rapidMultipleDeletes_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("12345")
		sut.setCursorPosition(at: 5)

		sut.simulateDeleteButtonPress() // "1234"
		sut.simulateDeleteButtonPress() // "123"
		sut.simulateDeleteButtonPress() // "12"
		sut.simulateDeleteButtonPress() // "1"
		sut.simulateDeleteButtonPress() // ""
		sut.simulateDeleteButtonPress() // should not crash on empty string

		XCTAssertEqual(sut.callNumberInput.text, "")
	}

	func test_deleteButtonPressed_concurrentTextChanges_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("123")
		sut.setCursorPosition(at: 3)

		sut.simulateDeleteButtonPress()

		sut.simulateNumberInput("")

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "")
	}

	func test_deleteButtonPressed_withNilSelectedTextRange_shouldDeleteCorrectly() {
		let sut = makeSUT()

		sut.simulateNumberInput("123")

		sut.callNumberInput.selectedTextRange = nil

		sut.simulateDeleteButtonPress()

		XCTAssertEqual(sut.callNumberInput.text, "12")
	}

	// MARK: - Add to Existing Contact Tests

	func test_addToExistingContact_withValidContact_shouldNotCrash() {
		let mockContact = MockCpcContact(contactId: "valid-contact-id")
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { contactId in
				return contactId == "valid-contact-id" ? (mockContact as CpcContact) : nil
			}
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		sut.simulateNumberInput("1234567890")
		
		// Create a mock contact preview with valid contact ID
		let mockContactPreview = MockCpcContactPreview(
			contactId: "valid-contact-id",
			contactInternalId: CpcContactInternalId(contactId: "valid-contact-id")
		)

		XCTAssertNoThrow {
			sut.onContactPreviewSelected(
				mockContactPreview,
				sender: nil
			)
		}
	}

	func test_addToExistingContact_withNilContactId_shouldNotCrash() {
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { _ in nil as CpcContact? }
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		sut.simulateNumberInput("1234567890")
		
		// Create a mock contact preview with nil contact ID
		let mockContactPreview = MockCpcContactPreview(
			contactId: nil,
			contactInternalId: nil
		)

		XCTAssertNoThrow {
			sut.onContactPreviewSelected(
				mockContactPreview,
				sender: nil
			)
		}
	}

	func test_addToExistingContact_withEmptyContactId_shouldNotCrash() {
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { _ in nil as CpcContact? }
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		sut.simulateNumberInput("1234567890")
		
		// Create a mock contact preview with empty contact ID
		let mockContactPreview = MockCpcContactPreview(
			contactId: "",
			contactInternalId: CpcContactInternalId(contactId: "")
		)

		XCTAssertNoThrow {
			sut.onContactPreviewSelected(
				mockContactPreview,
				
				sender: nil
			)
		}
	}

	func test_addToExistingContact_withValidContactIdButNilContact_shouldNotCrash() {
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { _ in nil as CpcContact? } // Simulate contact not found
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		sut.simulateNumberInput("1234567890")
		
		// Create a mock contact preview with valid ID but contact lookup returns nil
		let mockContactPreview = MockCpcContactPreview(
			contactId: "valid-contact-id",
			contactInternalId: CpcContactInternalId(contactId: "valid-contact-id")
		)

		XCTAssertNoThrow {
			sut.onContactPreviewSelected(
				mockContactPreview,
				
				sender: nil
			)
		}
	}

	func test_addToExistingContact_withNilContactInternalId_shouldNotCrash() {
		let mockContact = MockCpcContact(contactId: "valid-contact-id")
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { contactId in
				return contactId == "valid-contact-id" ? (mockContact as CpcContact) : nil
			}
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		sut.simulateNumberInput("1234567890")
		
		// Create a mock contact preview with valid contact ID but nil internal ID
		let mockContactPreview = MockCpcContactPreview(
			contactId: "valid-contact-id",
			contactInternalId: nil // This is the problematic scenario
		)

		XCTAssertNoThrow {
			sut.onContactPreviewSelected(
				mockContactPreview,
				
				sender: nil
			)
		}
		
		delay(0.3)
	}

	func test_addToExistingContact_withEmptyPhoneNumber_shouldNotCrash() {
		let mockContact = MockCpcContact(contactId: "valid-contact-id")
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { contactId in
				return contactId == "valid-contact-id" ? (mockContact as CpcContact) : nil
			}
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		// Empty phone number
		sut.simulateNumberInput("")
		
		let mockContactPreview = MockCpcContactPreview(
			contactId: "valid-contact-id",
			contactInternalId: CpcContactInternalId(contactId: "valid-contact-id")
		)

		XCTAssertNoThrow {
			sut.onContactPreviewSelected(
				mockContactPreview,
				
				sender: nil
			)
		}
	}

	func test_addToExistingContact_multipleCallsWithSameContact_shouldNotCrash() {
		let mockContact = MockCpcContact(contactId: "valid-contact-id")
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { contactId in
				return contactId == "valid-contact-id" ? (mockContact as CpcContact) : nil
			}
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		let mockContactPreview = MockCpcContactPreview(
			contactId: "valid-contact-id",
			contactInternalId: CpcContactInternalId(contactId: "valid-contact-id")
		)

		// Call multiple times to test for any state corruption
		for i in 1...5 {
			sut.simulateNumberInput("123456789\(i)")
			
			XCTAssertNoThrow {
				sut.onContactPreviewSelected(
					mockContactPreview,
					
					sender: nil
				)
			}
		}
	}

	func test_addToExistingContact_fromSearchResults_shouldNotCrash() {
		let mockContact = MockCpcContact(contactId: "search-contact-id")
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { contactId in
				return contactId == "search-contact-id" ? (mockContact as CpcContact) : nil
			}
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		sut.simulateNumberInput("1234567890")
		
		let mockContactPreview = MockCpcContactPreview(
			contactId: "search-contact-id",
			contactInternalId: CpcContactInternalId(contactId: "search-contact-id")
		)

		XCTAssertNoThrow {
			sut.onContactPreviewSelected(
				mockContactPreview,
				sender: nil
			)
		}
	}

	func test_addToExistingContact_withSpecialCharactersInPhoneNumber_shouldNotCrash() {
		let mockContact = MockCpcContact(contactId: "valid-contact-id")
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { contactId in
				return contactId == "valid-contact-id" ? (mockContact as CpcContact) : nil
			}
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		// Test with various special characters
		let specialNumbers = [
			"******-123-4567",
			"(*************",
			"************",
			"*67#123456789",
			"+44 20 7946 0958"
		]

		let mockContactPreview = MockCpcContactPreview(
			contactId: "valid-contact-id",
			contactInternalId: CpcContactInternalId(contactId: "valid-contact-id")
		)

		for number in specialNumbers {
			sut.simulateNumberInput(number)

			XCTAssertNoThrow {
				sut.onContactPreviewSelected(
					mockContactPreview,

					sender: nil
				)
			}
		}
	}

	func test_addToExistingContact_withNilCpcContactFromValidId_shouldCatchSentryCrash() {
		// This test reproduces the exact crash scenario from Sentry issues BRIOS-2730 and BRIOS-2723
		// where onGetCpcContact returns nil for a valid contact ID, causing NSInvalidArgumentException
		// when trying to create arrays/dictionaries with nil objects
		let mockCpcDependency = CpcDependency(
			onGetCpcContact: { _ in
				// Simulate the crash scenario: valid contact ID but onGetCpcContact returns nil
				return nil as CpcContact?
			}
		)
		let dependency = PhoneDependency(
			isAddNumberFeatureEnabled: true,
			cpc: mockCpcDependency
		)
		let sut = makeSUT(dependency: dependency)

		sut.simulateNumberInput("1234567890")

		// Create a mock contact preview with valid contact ID that should exist
		// but onGetCpcContact will return nil, triggering the crash scenario
		let mockContactPreview = MockCpcContactPreview(
			contactId: "existing-contact-id",
			contactInternalId: CpcContactInternalId(contactId: "existing-contact-id")
		)

		// This should reproduce the NSInvalidArgumentException crash:
		// "attempt to insert nil object from objects[0]" when creating arrays/dictionaries
		XCTAssertNoThrow {
			sut.onContactPreviewSelected(
				mockContactPreview,
				sender: nil
			)
		}
	}
}

// MARK: - Asserts

private extension PhoneIntegrationTests {
	func assertAddNumberContextMenu(
		for sut: PhoneViewController,
		file _: StaticString = #file,
		line _: UInt = #line
	) {
		XCTAssertNotNil(sut.currentActionSheet)

		let title = sut.currentActionSheet?.title
		XCTAssertEqual(title, NSLocalizedString("Add Number", comment: ""))
		let button1Text = sut.currentActionSheet?.buttons?.first?.text
		XCTAssertEqual(button1Text, NSLocalizedString("Create New Contact", comment: ""))
		let button2Text = sut.currentActionSheet?.buttons?[1].text
		XCTAssertEqual(button2Text, NSLocalizedString("Add to Existing Contact", comment: ""))
	}
}

// MARK: - System Under Tests (SUT) Factory

private extension PhoneIntegrationTests {
	func makeSUT(
		dependency: PhoneDependency = .init(),
		file: StaticString = #file,
		line: UInt = #line
	) -> PhoneViewController {
		let vc = PhoneViewController(dependency: dependency)

		assertNoMemoryLeaks(vc, file: file, line: line)

		vc.loadViewIfNeeded()

		return vc
	}

	func presentInWindow(_ sut: PhoneViewController) {
		let window = UIWindow(frame: UIScreen.main.bounds)
		window.rootViewController = sut
		window.makeKeyAndVisible()
	}

	func dismiss(_ sut: PhoneViewController) {
		sut.view.removeFromSuperview()

		if
			let windowScene = UIApplication.shared.connectedScenes
				.compactMap({ $0 as? UIWindowScene })
				.first(where: { $0.activationState == .foregroundActive }),
			let window = windowScene.windows.first(where: { $0.isKeyWindow }) {
			window.rootViewController = nil
			window.isHidden = true
		}

		delay(0.3)
	}

	func delay(_ seconds: TimeInterval) {
		RunLoop.current.run(until: Date(timeIntervalSinceNow: seconds))
	}
}

// MARK: - SUT Helpers

private extension PhoneViewController {
	func isAddNumberViewPresented() -> Bool {
		addNumberButton() != nil
	}

	func isAddNumberButtonHidden() -> Bool {
		addNumberButton()?.isHidden ?? false
	}

	func simulateNumberInput(_ number: String) {
		callNumberInput.text = number
	}

	func simulateAddNumberAction() {
		addNumberButton()?.sendActions(for: .touchUpInside)
	}

	func simulateKeyPadButtonPress(_ buttonId: ButtonId, character: unichar = 0) {
		buttonPressed(buttonId, forCharacter: character)
	}

	func simulateDeleteButtonPress() {
		buttonPressed(EButtonId_del, forCharacter: 0)
	}

	func setCursorPosition(at offset: Int) {
		setTextSelection(from: offset, to: offset)
	}

	func setTextSelection(from startOffset: Int, to endOffset: Int) {
		callNumberInput.becomeFirstResponder()
		if
			let startPosition = callNumberInput.position(
				from: callNumberInput.beginningOfDocument,
				offset: startOffset
			),
			let endPosition = callNumberInput.position(
				from: callNumberInput.beginningOfDocument,
				offset: endOffset
			) {
			callNumberInput.selectedTextRange = callNumberInput.textRange(
				from: startPosition,
				to: endPosition
			)
		}
	}

	private func addNumberButton() -> UIButton? {
		callNumberContainerView?.arrangedSubviews[0] as? UIButton
	}
}
